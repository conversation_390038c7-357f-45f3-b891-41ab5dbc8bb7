import {
  HostHeaderInputConfig,
  HostHeaderResolvedConfig,
} from "@aws-sdk/middleware-host-header";
import {
  QueueUrlInputConfig,
  QueueUrlResolvedConfig,
} from "@aws-sdk/middleware-sdk-sqs";
import {
  UserAgentInputConfig,
  UserAgentResolvedConfig,
} from "@aws-sdk/middleware-user-agent";
import {
  RegionInputConfig,
  RegionResolvedConfig,
} from "@smithy/config-resolver";
import {
  EndpointInputConfig,
  EndpointResolvedConfig,
} from "@smithy/middleware-endpoint";
import {
  RetryInputConfig,
  RetryResolvedConfig,
} from "@smithy/middleware-retry";
import { HttpHandlerUserInput as __HttpHandlerUserInput } from "@smithy/protocol-http";
import {
  Client as __Client,
  DefaultsMode as __DefaultsMode,
  SmithyConfiguration as __SmithyConfiguration,
  SmithyResolvedConfiguration as __SmithyResolvedConfiguration,
} from "@smithy/smithy-client";
import {
  AwsCredentialIdentityProvider,
  BodyLengthCalculator as __BodyLengthCalculator,
  CheckOptionalClientConfig as __CheckOptionalClientConfig,
  ChecksumConstructor as __ChecksumConstructor,
  Decoder as __Decoder,
  Encoder as __Encoder,
  HashConstructor as __HashConstructor,
  HttpHandlerOptions as __HttpHandlerOptions,
  Logger as __Logger,
  Provider as __Provider,
  Provider,
  StreamCollector as __StreamCollector,
  UrlParser as __UrlParser,
  UserAgent as __UserAgent,
} from "@smithy/types";
import {
  HttpAuthSchemeInputConfig,
  HttpAuthSchemeResolvedConfig,
} from "./auth/httpAuthSchemeProvider";
import {
  AddPermissionCommandInput,
  AddPermissionCommandOutput,
} from "./commands/AddPermissionCommand";
import {
  CancelMessageMoveTaskCommandInput,
  CancelMessageMoveTaskCommandOutput,
} from "./commands/CancelMessageMoveTaskCommand";
import {
  ChangeMessageVisibilityBatchCommandInput,
  ChangeMessageVisibilityBatchCommandOutput,
} from "./commands/ChangeMessageVisibilityBatchCommand";
import {
  ChangeMessageVisibilityCommandInput,
  ChangeMessageVisibilityCommandOutput,
} from "./commands/ChangeMessageVisibilityCommand";
import {
  CreateQueueCommandInput,
  CreateQueueCommandOutput,
} from "./commands/CreateQueueCommand";
import {
  DeleteMessageBatchCommandInput,
  DeleteMessageBatchCommandOutput,
} from "./commands/DeleteMessageBatchCommand";
import {
  DeleteMessageCommandInput,
  DeleteMessageCommandOutput,
} from "./commands/DeleteMessageCommand";
import {
  DeleteQueueCommandInput,
  DeleteQueueCommandOutput,
} from "./commands/DeleteQueueCommand";
import {
  GetQueueAttributesCommandInput,
  GetQueueAttributesCommandOutput,
} from "./commands/GetQueueAttributesCommand";
import {
  GetQueueUrlCommandInput,
  GetQueueUrlCommandOutput,
} from "./commands/GetQueueUrlCommand";
import {
  ListDeadLetterSourceQueuesCommandInput,
  ListDeadLetterSourceQueuesCommandOutput,
} from "./commands/ListDeadLetterSourceQueuesCommand";
import {
  ListMessageMoveTasksCommandInput,
  ListMessageMoveTasksCommandOutput,
} from "./commands/ListMessageMoveTasksCommand";
import {
  ListQueuesCommandInput,
  ListQueuesCommandOutput,
} from "./commands/ListQueuesCommand";
import {
  ListQueueTagsCommandInput,
  ListQueueTagsCommandOutput,
} from "./commands/ListQueueTagsCommand";
import {
  PurgeQueueCommandInput,
  PurgeQueueCommandOutput,
} from "./commands/PurgeQueueCommand";
import {
  ReceiveMessageCommandInput,
  ReceiveMessageCommandOutput,
} from "./commands/ReceiveMessageCommand";
import {
  RemovePermissionCommandInput,
  RemovePermissionCommandOutput,
} from "./commands/RemovePermissionCommand";
import {
  SendMessageBatchCommandInput,
  SendMessageBatchCommandOutput,
} from "./commands/SendMessageBatchCommand";
import {
  SendMessageCommandInput,
  SendMessageCommandOutput,
} from "./commands/SendMessageCommand";
import {
  SetQueueAttributesCommandInput,
  SetQueueAttributesCommandOutput,
} from "./commands/SetQueueAttributesCommand";
import {
  StartMessageMoveTaskCommandInput,
  StartMessageMoveTaskCommandOutput,
} from "./commands/StartMessageMoveTaskCommand";
import {
  TagQueueCommandInput,
  TagQueueCommandOutput,
} from "./commands/TagQueueCommand";
import {
  UntagQueueCommandInput,
  UntagQueueCommandOutput,
} from "./commands/UntagQueueCommand";
import {
  ClientInputEndpointParameters,
  ClientResolvedEndpointParameters,
  EndpointParameters,
} from "./endpoint/EndpointParameters";
import { RuntimeExtension, RuntimeExtensionsConfig } from "./runtimeExtensions";
export { __Client };
export type ServiceInputTypes =
  | AddPermissionCommandInput
  | CancelMessageMoveTaskCommandInput
  | ChangeMessageVisibilityBatchCommandInput
  | ChangeMessageVisibilityCommandInput
  | CreateQueueCommandInput
  | DeleteMessageBatchCommandInput
  | DeleteMessageCommandInput
  | DeleteQueueCommandInput
  | GetQueueAttributesCommandInput
  | GetQueueUrlCommandInput
  | ListDeadLetterSourceQueuesCommandInput
  | ListMessageMoveTasksCommandInput
  | ListQueueTagsCommandInput
  | ListQueuesCommandInput
  | PurgeQueueCommandInput
  | ReceiveMessageCommandInput
  | RemovePermissionCommandInput
  | SendMessageBatchCommandInput
  | SendMessageCommandInput
  | SetQueueAttributesCommandInput
  | StartMessageMoveTaskCommandInput
  | TagQueueCommandInput
  | UntagQueueCommandInput;
export type ServiceOutputTypes =
  | AddPermissionCommandOutput
  | CancelMessageMoveTaskCommandOutput
  | ChangeMessageVisibilityBatchCommandOutput
  | ChangeMessageVisibilityCommandOutput
  | CreateQueueCommandOutput
  | DeleteMessageBatchCommandOutput
  | DeleteMessageCommandOutput
  | DeleteQueueCommandOutput
  | GetQueueAttributesCommandOutput
  | GetQueueUrlCommandOutput
  | ListDeadLetterSourceQueuesCommandOutput
  | ListMessageMoveTasksCommandOutput
  | ListQueueTagsCommandOutput
  | ListQueuesCommandOutput
  | PurgeQueueCommandOutput
  | ReceiveMessageCommandOutput
  | RemovePermissionCommandOutput
  | SendMessageBatchCommandOutput
  | SendMessageCommandOutput
  | SetQueueAttributesCommandOutput
  | StartMessageMoveTaskCommandOutput
  | TagQueueCommandOutput
  | UntagQueueCommandOutput;
export interface ClientDefaults
  extends Partial<__SmithyConfiguration<__HttpHandlerOptions>> {
  requestHandler?: __HttpHandlerUserInput;
  sha256?: __ChecksumConstructor | __HashConstructor;
  urlParser?: __UrlParser;
  bodyLengthChecker?: __BodyLengthCalculator;
  streamCollector?: __StreamCollector;
  base64Decoder?: __Decoder;
  base64Encoder?: __Encoder;
  utf8Decoder?: __Decoder;
  utf8Encoder?: __Encoder;
  runtime?: string;
  disableHostPrefix?: boolean;
  serviceId?: string;
  useDualstackEndpoint?: boolean | __Provider<boolean>;
  useFipsEndpoint?: boolean | __Provider<boolean>;
  region?: string | __Provider<string>;
  profile?: string;
  defaultUserAgentProvider?: Provider<__UserAgent>;
  credentialDefaultProvider?: (input: any) => AwsCredentialIdentityProvider;
  md5?: __ChecksumConstructor | __HashConstructor | false;
  maxAttempts?: number | __Provider<number>;
  retryMode?: string | __Provider<string>;
  logger?: __Logger;
  extensions?: RuntimeExtension[];
  defaultsMode?: __DefaultsMode | __Provider<__DefaultsMode>;
}
export type SQSClientConfigType = Partial<
  __SmithyConfiguration<__HttpHandlerOptions>
> &
  ClientDefaults &
  UserAgentInputConfig &
  RetryInputConfig &
  RegionInputConfig &
  HostHeaderInputConfig &
  EndpointInputConfig<EndpointParameters> &
  QueueUrlInputConfig &
  HttpAuthSchemeInputConfig &
  ClientInputEndpointParameters;
export interface SQSClientConfig extends SQSClientConfigType {}
export type SQSClientResolvedConfigType =
  __SmithyResolvedConfiguration<__HttpHandlerOptions> &
    Required<ClientDefaults> &
    RuntimeExtensionsConfig &
    UserAgentResolvedConfig &
    RetryResolvedConfig &
    RegionResolvedConfig &
    HostHeaderResolvedConfig &
    EndpointResolvedConfig<EndpointParameters> &
    QueueUrlResolvedConfig &
    HttpAuthSchemeResolvedConfig &
    ClientResolvedEndpointParameters;
export interface SQSClientResolvedConfig extends SQSClientResolvedConfigType {}
export declare class SQSClient extends __Client<
  __HttpHandlerOptions,
  ServiceInputTypes,
  ServiceOutputTypes,
  SQSClientResolvedConfig
> {
  readonly config: SQSClientResolvedConfig;
  constructor(...[configuration]: __CheckOptionalClientConfig<SQSClientConfig>);
  destroy(): void;
}
