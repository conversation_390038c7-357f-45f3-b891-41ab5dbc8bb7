import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { ListDeadLetterSourceQueuesRequest, ListDeadLetterSourceQueuesResult } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SQSClientResolvedConfig } from "../SQSClient";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link ListDeadLetterSourceQueuesCommand}.
 */
export interface ListDeadLetterSourceQueuesCommandInput extends ListDeadLetterSourceQueuesRequest {
}
/**
 * @public
 *
 * The output of {@link ListDeadLetterSourceQueuesCommand}.
 */
export interface ListDeadLetterSourceQueuesCommandOutput extends ListDeadLetterSourceQueuesResult, __MetadataBearer {
}
declare const ListDeadLetterSourceQueuesCommand_base: {
    new (input: ListDeadLetterSourceQueuesCommandInput): import("@smithy/smithy-client").CommandImpl<ListDeadLetterSourceQueuesCommandInput, ListDeadLetterSourceQueuesCommandOutput, SQSClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: ListDeadLetterSourceQueuesCommandInput): import("@smithy/smithy-client").CommandImpl<ListDeadLetterSourceQueuesCommandInput, ListDeadLetterSourceQueuesCommandOutput, SQSClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Returns a list of your queues that have the <code>RedrivePolicy</code> queue attribute
 *             configured with a dead-letter queue.</p>
 *          <p> The <code>ListDeadLetterSourceQueues</code> methods supports pagination. Set
 *             parameter <code>MaxResults</code> in the request to specify the maximum number of
 *             results to be returned in the response. If you do not set <code>MaxResults</code>, the
 *             response includes a maximum of 1,000 results. If you set <code>MaxResults</code> and
 *             there are additional results to display, the response includes a value for
 *                 <code>NextToken</code>. Use <code>NextToken</code> as a parameter in your next
 *             request to <code>ListDeadLetterSourceQueues</code> to receive the next page of results. </p>
 *          <p>For more information about using dead-letter queues, see <a href="https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-dead-letter-queues.html">Using Amazon SQS Dead-Letter Queues</a> in the <i>Amazon SQS Developer
 *                 Guide</i>.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SQSClient, ListDeadLetterSourceQueuesCommand } from "@aws-sdk/client-sqs"; // ES Modules import
 * // const { SQSClient, ListDeadLetterSourceQueuesCommand } = require("@aws-sdk/client-sqs"); // CommonJS import
 * const client = new SQSClient(config);
 * const input = { // ListDeadLetterSourceQueuesRequest
 *   QueueUrl: "STRING_VALUE", // required
 *   NextToken: "STRING_VALUE",
 *   MaxResults: Number("int"),
 * };
 * const command = new ListDeadLetterSourceQueuesCommand(input);
 * const response = await client.send(command);
 * // { // ListDeadLetterSourceQueuesResult
 * //   queueUrls: [ // QueueUrlList // required
 * //     "STRING_VALUE",
 * //   ],
 * //   NextToken: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param ListDeadLetterSourceQueuesCommandInput - {@link ListDeadLetterSourceQueuesCommandInput}
 * @returns {@link ListDeadLetterSourceQueuesCommandOutput}
 * @see {@link ListDeadLetterSourceQueuesCommandInput} for command's `input` shape.
 * @see {@link ListDeadLetterSourceQueuesCommandOutput} for command's `response` shape.
 * @see {@link SQSClientResolvedConfig | config} for SQSClient's `config` shape.
 *
 * @throws {@link InvalidAddress} (client fault)
 *  <p>The specified ID is invalid.</p>
 *
 * @throws {@link InvalidSecurity} (client fault)
 *  <p>The request was not made over HTTPS or did not use SigV4 for signing.</p>
 *
 * @throws {@link QueueDoesNotExist} (client fault)
 *  <p>Ensure that the <code>QueueUrl</code> is correct and that the queue has not been
 *             deleted.</p>
 *
 * @throws {@link RequestThrottled} (client fault)
 *  <p>The request was denied due to request throttling.</p>
 *          <ul>
 *             <li>
 *                <p>Exceeds the permitted request rate for the queue or for the recipient of the
 *                     request.</p>
 *             </li>
 *             <li>
 *                <p>Ensure that the request rate is within the Amazon SQS limits for
 *                     sending messages. For more information, see <a href="https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-quotas.html#quotas-requests">Amazon SQS quotas</a> in the <i>Amazon SQS
 *                         Developer Guide</i>.</p>
 *             </li>
 *          </ul>
 *
 * @throws {@link UnsupportedOperation} (client fault)
 *  <p>Error code 400. Unsupported operation.</p>
 *
 * @throws {@link SQSServiceException}
 * <p>Base exception class for all service exceptions from SQS service.</p>
 *
 *
 * @public
 */
export declare class ListDeadLetterSourceQueuesCommand extends ListDeadLetterSourceQueuesCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: ListDeadLetterSourceQueuesRequest;
            output: ListDeadLetterSourceQueuesResult;
        };
        sdk: {
            input: ListDeadLetterSourceQueuesCommandInput;
            output: ListDeadLetterSourceQueuesCommandOutput;
        };
    };
}
