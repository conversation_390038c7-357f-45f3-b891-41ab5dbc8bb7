"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  AddPermissionCommand: () => AddPermissionCommand,
  BatchEntryIdsNotDistinct: () => BatchEntryIdsNotDistinct,
  BatchRequestTooLong: () => BatchRequestTooLong,
  CancelMessageMoveTaskCommand: () => CancelMessageMoveTaskCommand,
  ChangeMessageVisibilityBatchCommand: () => ChangeMessageVisibilityBatchCommand,
  ChangeMessageVisibilityCommand: () => ChangeMessageVisibilityCommand,
  CreateQueueCommand: () => CreateQueueCommand,
  DeleteMessageBatchCommand: () => DeleteMessageBatchCommand,
  DeleteMessageCommand: () => DeleteMessageCommand,
  DeleteQueueCommand: () => DeleteQueueCommand,
  EmptyBatchRequest: () => EmptyBatchRequest,
  GetQueueAttributesCommand: () => GetQueueAttributesCommand,
  GetQueueUrlCommand: () => GetQueueUrlCommand,
  InvalidAddress: () => InvalidAddress,
  InvalidAttributeName: () => InvalidAttributeName,
  InvalidAttributeValue: () => InvalidAttributeValue,
  InvalidBatchEntryId: () => InvalidBatchEntryId,
  InvalidIdFormat: () => InvalidIdFormat,
  InvalidMessageContents: () => InvalidMessageContents,
  InvalidSecurity: () => InvalidSecurity,
  KmsAccessDenied: () => KmsAccessDenied,
  KmsDisabled: () => KmsDisabled,
  KmsInvalidKeyUsage: () => KmsInvalidKeyUsage,
  KmsInvalidState: () => KmsInvalidState,
  KmsNotFound: () => KmsNotFound,
  KmsOptInRequired: () => KmsOptInRequired,
  KmsThrottled: () => KmsThrottled,
  ListDeadLetterSourceQueuesCommand: () => ListDeadLetterSourceQueuesCommand,
  ListMessageMoveTasksCommand: () => ListMessageMoveTasksCommand,
  ListQueueTagsCommand: () => ListQueueTagsCommand,
  ListQueuesCommand: () => ListQueuesCommand,
  MessageNotInflight: () => MessageNotInflight,
  MessageSystemAttributeName: () => MessageSystemAttributeName,
  MessageSystemAttributeNameForSends: () => MessageSystemAttributeNameForSends,
  OverLimit: () => OverLimit,
  PurgeQueueCommand: () => PurgeQueueCommand,
  PurgeQueueInProgress: () => PurgeQueueInProgress,
  QueueAttributeName: () => QueueAttributeName,
  QueueDeletedRecently: () => QueueDeletedRecently,
  QueueDoesNotExist: () => QueueDoesNotExist,
  QueueNameExists: () => QueueNameExists,
  ReceiptHandleIsInvalid: () => ReceiptHandleIsInvalid,
  ReceiveMessageCommand: () => ReceiveMessageCommand,
  RemovePermissionCommand: () => RemovePermissionCommand,
  RequestThrottled: () => RequestThrottled,
  ResourceNotFoundException: () => ResourceNotFoundException,
  SQS: () => SQS,
  SQSClient: () => SQSClient,
  SQSServiceException: () => SQSServiceException,
  SendMessageBatchCommand: () => SendMessageBatchCommand,
  SendMessageCommand: () => SendMessageCommand,
  SetQueueAttributesCommand: () => SetQueueAttributesCommand,
  StartMessageMoveTaskCommand: () => StartMessageMoveTaskCommand,
  TagQueueCommand: () => TagQueueCommand,
  TooManyEntriesInBatchRequest: () => TooManyEntriesInBatchRequest,
  UnsupportedOperation: () => UnsupportedOperation,
  UntagQueueCommand: () => UntagQueueCommand,
  __Client: () => import_smithy_client.Client,
  paginateListDeadLetterSourceQueues: () => paginateListDeadLetterSourceQueues,
  paginateListQueues: () => paginateListQueues
});
module.exports = __toCommonJS(index_exports);

// src/SQSClient.ts
var import_middleware_host_header = require("@aws-sdk/middleware-host-header");
var import_middleware_logger = require("@aws-sdk/middleware-logger");
var import_middleware_recursion_detection = require("@aws-sdk/middleware-recursion-detection");
var import_middleware_sdk_sqs = require("@aws-sdk/middleware-sdk-sqs");
var import_middleware_user_agent = require("@aws-sdk/middleware-user-agent");
var import_config_resolver = require("@smithy/config-resolver");
var import_core = require("@smithy/core");
var import_middleware_content_length = require("@smithy/middleware-content-length");
var import_middleware_endpoint = require("@smithy/middleware-endpoint");
var import_middleware_retry = require("@smithy/middleware-retry");

var import_httpAuthSchemeProvider = require("./auth/httpAuthSchemeProvider");

// src/endpoint/EndpointParameters.ts
var resolveClientEndpointParameters = /* @__PURE__ */ __name((options) => {
  return Object.assign(options, {
    useDualstackEndpoint: options.useDualstackEndpoint ?? false,
    useFipsEndpoint: options.useFipsEndpoint ?? false,
    defaultSigningName: "sqs"
  });
}, "resolveClientEndpointParameters");
var commonParams = {
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};

// src/SQSClient.ts
var import_runtimeConfig = require("././runtimeConfig");

// src/runtimeExtensions.ts
var import_region_config_resolver = require("@aws-sdk/region-config-resolver");
var import_protocol_http = require("@smithy/protocol-http");
var import_smithy_client = require("@smithy/smithy-client");

// src/auth/httpAuthExtensionConfiguration.ts
var getHttpAuthExtensionConfiguration = /* @__PURE__ */ __name((runtimeConfig) => {
  const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;
  let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;
  let _credentials = runtimeConfig.credentials;
  return {
    setHttpAuthScheme(httpAuthScheme) {
      const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);
      if (index === -1) {
        _httpAuthSchemes.push(httpAuthScheme);
      } else {
        _httpAuthSchemes.splice(index, 1, httpAuthScheme);
      }
    },
    httpAuthSchemes() {
      return _httpAuthSchemes;
    },
    setHttpAuthSchemeProvider(httpAuthSchemeProvider) {
      _httpAuthSchemeProvider = httpAuthSchemeProvider;
    },
    httpAuthSchemeProvider() {
      return _httpAuthSchemeProvider;
    },
    setCredentials(credentials) {
      _credentials = credentials;
    },
    credentials() {
      return _credentials;
    }
  };
}, "getHttpAuthExtensionConfiguration");
var resolveHttpAuthRuntimeConfig = /* @__PURE__ */ __name((config) => {
  return {
    httpAuthSchemes: config.httpAuthSchemes(),
    httpAuthSchemeProvider: config.httpAuthSchemeProvider(),
    credentials: config.credentials()
  };
}, "resolveHttpAuthRuntimeConfig");

// src/runtimeExtensions.ts
var resolveRuntimeExtensions = /* @__PURE__ */ __name((runtimeConfig, extensions) => {
  const extensionConfiguration = Object.assign(
    (0, import_region_config_resolver.getAwsRegionExtensionConfiguration)(runtimeConfig),
    (0, import_smithy_client.getDefaultExtensionConfiguration)(runtimeConfig),
    (0, import_protocol_http.getHttpHandlerExtensionConfiguration)(runtimeConfig),
    getHttpAuthExtensionConfiguration(runtimeConfig)
  );
  extensions.forEach((extension) => extension.configure(extensionConfiguration));
  return Object.assign(
    runtimeConfig,
    (0, import_region_config_resolver.resolveAwsRegionExtensionConfiguration)(extensionConfiguration),
    (0, import_smithy_client.resolveDefaultRuntimeConfig)(extensionConfiguration),
    (0, import_protocol_http.resolveHttpHandlerRuntimeConfig)(extensionConfiguration),
    resolveHttpAuthRuntimeConfig(extensionConfiguration)
  );
}, "resolveRuntimeExtensions");

// src/SQSClient.ts
var SQSClient = class extends import_smithy_client.Client {
  static {
    __name(this, "SQSClient");
  }
  /**
   * The resolved configuration of SQSClient class. This is resolved and normalized from the {@link SQSClientConfig | constructor configuration interface}.
   */
  config;
  constructor(...[configuration]) {
    const _config_0 = (0, import_runtimeConfig.getRuntimeConfig)(configuration || {});
    super(_config_0);
    this.initConfig = _config_0;
    const _config_1 = resolveClientEndpointParameters(_config_0);
    const _config_2 = (0, import_middleware_user_agent.resolveUserAgentConfig)(_config_1);
    const _config_3 = (0, import_middleware_retry.resolveRetryConfig)(_config_2);
    const _config_4 = (0, import_config_resolver.resolveRegionConfig)(_config_3);
    const _config_5 = (0, import_middleware_host_header.resolveHostHeaderConfig)(_config_4);
    const _config_6 = (0, import_middleware_endpoint.resolveEndpointConfig)(_config_5);
    const _config_7 = (0, import_middleware_sdk_sqs.resolveQueueUrlConfig)(_config_6);
    const _config_8 = (0, import_httpAuthSchemeProvider.resolveHttpAuthSchemeConfig)(_config_7);
    const _config_9 = resolveRuntimeExtensions(_config_8, configuration?.extensions || []);
    this.config = _config_9;
    this.middlewareStack.use((0, import_middleware_user_agent.getUserAgentPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_retry.getRetryPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_content_length.getContentLengthPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_host_header.getHostHeaderPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_logger.getLoggerPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_recursion_detection.getRecursionDetectionPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_sdk_sqs.getQueueUrlPlugin)(this.config));
    this.middlewareStack.use(
      (0, import_core.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {
        httpAuthSchemeParametersProvider: import_httpAuthSchemeProvider.defaultSQSHttpAuthSchemeParametersProvider,
        identityProviderConfigProvider: /* @__PURE__ */ __name(async (config) => new import_core.DefaultIdentityProviderConfig({
          "aws.auth#sigv4": config.credentials
        }), "identityProviderConfigProvider")
      })
    );
    this.middlewareStack.use((0, import_core.getHttpSigningPlugin)(this.config));
  }
  /**
   * Destroy underlying resources, like sockets. It's usually not necessary to do this.
   * However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
   * Otherwise, sockets might stay open for quite a long time before the server terminates them.
   */
  destroy() {
    super.destroy();
  }
};

// src/SQS.ts


// src/commands/AddPermissionCommand.ts

var import_middleware_serde = require("@smithy/middleware-serde");


// src/protocols/Aws_json1_0.ts
var import_core2 = require("@aws-sdk/core");



// src/models/SQSServiceException.ts

var SQSServiceException = class _SQSServiceException extends import_smithy_client.ServiceException {
  static {
    __name(this, "SQSServiceException");
  }
  /**
   * @internal
   */
  constructor(options) {
    super(options);
    Object.setPrototypeOf(this, _SQSServiceException.prototype);
  }
};

// src/models/models_0.ts
var InvalidAddress = class _InvalidAddress extends SQSServiceException {
  static {
    __name(this, "InvalidAddress");
  }
  name = "InvalidAddress";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidAddress",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidAddress.prototype);
  }
};
var InvalidSecurity = class _InvalidSecurity extends SQSServiceException {
  static {
    __name(this, "InvalidSecurity");
  }
  name = "InvalidSecurity";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidSecurity",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidSecurity.prototype);
  }
};
var OverLimit = class _OverLimit extends SQSServiceException {
  static {
    __name(this, "OverLimit");
  }
  name = "OverLimit";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "OverLimit",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _OverLimit.prototype);
  }
};
var QueueDoesNotExist = class _QueueDoesNotExist extends SQSServiceException {
  static {
    __name(this, "QueueDoesNotExist");
  }
  name = "QueueDoesNotExist";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "QueueDoesNotExist",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _QueueDoesNotExist.prototype);
  }
};
var RequestThrottled = class _RequestThrottled extends SQSServiceException {
  static {
    __name(this, "RequestThrottled");
  }
  name = "RequestThrottled";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "RequestThrottled",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _RequestThrottled.prototype);
  }
};
var UnsupportedOperation = class _UnsupportedOperation extends SQSServiceException {
  static {
    __name(this, "UnsupportedOperation");
  }
  name = "UnsupportedOperation";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UnsupportedOperation",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UnsupportedOperation.prototype);
  }
};
var ResourceNotFoundException = class _ResourceNotFoundException extends SQSServiceException {
  static {
    __name(this, "ResourceNotFoundException");
  }
  name = "ResourceNotFoundException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ResourceNotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ResourceNotFoundException.prototype);
  }
};
var MessageNotInflight = class _MessageNotInflight extends SQSServiceException {
  static {
    __name(this, "MessageNotInflight");
  }
  name = "MessageNotInflight";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "MessageNotInflight",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _MessageNotInflight.prototype);
  }
};
var ReceiptHandleIsInvalid = class _ReceiptHandleIsInvalid extends SQSServiceException {
  static {
    __name(this, "ReceiptHandleIsInvalid");
  }
  name = "ReceiptHandleIsInvalid";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ReceiptHandleIsInvalid",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ReceiptHandleIsInvalid.prototype);
  }
};
var BatchEntryIdsNotDistinct = class _BatchEntryIdsNotDistinct extends SQSServiceException {
  static {
    __name(this, "BatchEntryIdsNotDistinct");
  }
  name = "BatchEntryIdsNotDistinct";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "BatchEntryIdsNotDistinct",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _BatchEntryIdsNotDistinct.prototype);
  }
};
var EmptyBatchRequest = class _EmptyBatchRequest extends SQSServiceException {
  static {
    __name(this, "EmptyBatchRequest");
  }
  name = "EmptyBatchRequest";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "EmptyBatchRequest",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _EmptyBatchRequest.prototype);
  }
};
var InvalidBatchEntryId = class _InvalidBatchEntryId extends SQSServiceException {
  static {
    __name(this, "InvalidBatchEntryId");
  }
  name = "InvalidBatchEntryId";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidBatchEntryId",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidBatchEntryId.prototype);
  }
};
var TooManyEntriesInBatchRequest = class _TooManyEntriesInBatchRequest extends SQSServiceException {
  static {
    __name(this, "TooManyEntriesInBatchRequest");
  }
  name = "TooManyEntriesInBatchRequest";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "TooManyEntriesInBatchRequest",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _TooManyEntriesInBatchRequest.prototype);
  }
};
var QueueAttributeName = {
  All: "All",
  ApproximateNumberOfMessages: "ApproximateNumberOfMessages",
  ApproximateNumberOfMessagesDelayed: "ApproximateNumberOfMessagesDelayed",
  ApproximateNumberOfMessagesNotVisible: "ApproximateNumberOfMessagesNotVisible",
  ContentBasedDeduplication: "ContentBasedDeduplication",
  CreatedTimestamp: "CreatedTimestamp",
  DeduplicationScope: "DeduplicationScope",
  DelaySeconds: "DelaySeconds",
  FifoQueue: "FifoQueue",
  FifoThroughputLimit: "FifoThroughputLimit",
  KmsDataKeyReusePeriodSeconds: "KmsDataKeyReusePeriodSeconds",
  KmsMasterKeyId: "KmsMasterKeyId",
  LastModifiedTimestamp: "LastModifiedTimestamp",
  MaximumMessageSize: "MaximumMessageSize",
  MessageRetentionPeriod: "MessageRetentionPeriod",
  Policy: "Policy",
  QueueArn: "QueueArn",
  ReceiveMessageWaitTimeSeconds: "ReceiveMessageWaitTimeSeconds",
  RedriveAllowPolicy: "RedriveAllowPolicy",
  RedrivePolicy: "RedrivePolicy",
  SqsManagedSseEnabled: "SqsManagedSseEnabled",
  VisibilityTimeout: "VisibilityTimeout"
};
var InvalidAttributeName = class _InvalidAttributeName extends SQSServiceException {
  static {
    __name(this, "InvalidAttributeName");
  }
  name = "InvalidAttributeName";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidAttributeName",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidAttributeName.prototype);
  }
};
var InvalidAttributeValue = class _InvalidAttributeValue extends SQSServiceException {
  static {
    __name(this, "InvalidAttributeValue");
  }
  name = "InvalidAttributeValue";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidAttributeValue",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidAttributeValue.prototype);
  }
};
var QueueDeletedRecently = class _QueueDeletedRecently extends SQSServiceException {
  static {
    __name(this, "QueueDeletedRecently");
  }
  name = "QueueDeletedRecently";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "QueueDeletedRecently",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _QueueDeletedRecently.prototype);
  }
};
var QueueNameExists = class _QueueNameExists extends SQSServiceException {
  static {
    __name(this, "QueueNameExists");
  }
  name = "QueueNameExists";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "QueueNameExists",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _QueueNameExists.prototype);
  }
};
var InvalidIdFormat = class _InvalidIdFormat extends SQSServiceException {
  static {
    __name(this, "InvalidIdFormat");
  }
  name = "InvalidIdFormat";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidIdFormat",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidIdFormat.prototype);
  }
};
var PurgeQueueInProgress = class _PurgeQueueInProgress extends SQSServiceException {
  static {
    __name(this, "PurgeQueueInProgress");
  }
  name = "PurgeQueueInProgress";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "PurgeQueueInProgress",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _PurgeQueueInProgress.prototype);
  }
};
var KmsAccessDenied = class _KmsAccessDenied extends SQSServiceException {
  static {
    __name(this, "KmsAccessDenied");
  }
  name = "KmsAccessDenied";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "KmsAccessDenied",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _KmsAccessDenied.prototype);
  }
};
var KmsDisabled = class _KmsDisabled extends SQSServiceException {
  static {
    __name(this, "KmsDisabled");
  }
  name = "KmsDisabled";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "KmsDisabled",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _KmsDisabled.prototype);
  }
};
var KmsInvalidKeyUsage = class _KmsInvalidKeyUsage extends SQSServiceException {
  static {
    __name(this, "KmsInvalidKeyUsage");
  }
  name = "KmsInvalidKeyUsage";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "KmsInvalidKeyUsage",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _KmsInvalidKeyUsage.prototype);
  }
};
var KmsInvalidState = class _KmsInvalidState extends SQSServiceException {
  static {
    __name(this, "KmsInvalidState");
  }
  name = "KmsInvalidState";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "KmsInvalidState",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _KmsInvalidState.prototype);
  }
};
var KmsNotFound = class _KmsNotFound extends SQSServiceException {
  static {
    __name(this, "KmsNotFound");
  }
  name = "KmsNotFound";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "KmsNotFound",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _KmsNotFound.prototype);
  }
};
var KmsOptInRequired = class _KmsOptInRequired extends SQSServiceException {
  static {
    __name(this, "KmsOptInRequired");
  }
  name = "KmsOptInRequired";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "KmsOptInRequired",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _KmsOptInRequired.prototype);
  }
};
var KmsThrottled = class _KmsThrottled extends SQSServiceException {
  static {
    __name(this, "KmsThrottled");
  }
  name = "KmsThrottled";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "KmsThrottled",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _KmsThrottled.prototype);
  }
};
var MessageSystemAttributeName = {
  AWSTraceHeader: "AWSTraceHeader",
  All: "All",
  ApproximateFirstReceiveTimestamp: "ApproximateFirstReceiveTimestamp",
  ApproximateReceiveCount: "ApproximateReceiveCount",
  DeadLetterQueueSourceArn: "DeadLetterQueueSourceArn",
  MessageDeduplicationId: "MessageDeduplicationId",
  MessageGroupId: "MessageGroupId",
  SenderId: "SenderId",
  SentTimestamp: "SentTimestamp",
  SequenceNumber: "SequenceNumber"
};
var InvalidMessageContents = class _InvalidMessageContents extends SQSServiceException {
  static {
    __name(this, "InvalidMessageContents");
  }
  name = "InvalidMessageContents";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidMessageContents",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidMessageContents.prototype);
  }
};
var MessageSystemAttributeNameForSends = {
  AWSTraceHeader: "AWSTraceHeader"
};
var BatchRequestTooLong = class _BatchRequestTooLong extends SQSServiceException {
  static {
    __name(this, "BatchRequestTooLong");
  }
  name = "BatchRequestTooLong";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "BatchRequestTooLong",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _BatchRequestTooLong.prototype);
  }
};

// src/protocols/Aws_json1_0.ts
var se_AddPermissionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AddPermission");
  let body;
  body = JSON.stringify(se_AddPermissionRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AddPermissionCommand");
var se_CancelMessageMoveTaskCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CancelMessageMoveTask");
  let body;
  body = JSON.stringify(se_CancelMessageMoveTaskRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CancelMessageMoveTaskCommand");
var se_ChangeMessageVisibilityCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ChangeMessageVisibility");
  let body;
  body = JSON.stringify(se_ChangeMessageVisibilityRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ChangeMessageVisibilityCommand");
var se_ChangeMessageVisibilityBatchCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ChangeMessageVisibilityBatch");
  let body;
  body = JSON.stringify(se_ChangeMessageVisibilityBatchRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ChangeMessageVisibilityBatchCommand");
var se_CreateQueueCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateQueue");
  let body;
  body = JSON.stringify(se_CreateQueueRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateQueueCommand");
var se_DeleteMessageCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteMessage");
  let body;
  body = JSON.stringify(se_DeleteMessageRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteMessageCommand");
var se_DeleteMessageBatchCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteMessageBatch");
  let body;
  body = JSON.stringify(se_DeleteMessageBatchRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteMessageBatchCommand");
var se_DeleteQueueCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteQueue");
  let body;
  body = JSON.stringify(se_DeleteQueueRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteQueueCommand");
var se_GetQueueAttributesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetQueueAttributes");
  let body;
  body = JSON.stringify(se_GetQueueAttributesRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetQueueAttributesCommand");
var se_GetQueueUrlCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetQueueUrl");
  let body;
  body = JSON.stringify(se_GetQueueUrlRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetQueueUrlCommand");
var se_ListDeadLetterSourceQueuesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListDeadLetterSourceQueues");
  let body;
  body = JSON.stringify(se_ListDeadLetterSourceQueuesRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListDeadLetterSourceQueuesCommand");
var se_ListMessageMoveTasksCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListMessageMoveTasks");
  let body;
  body = JSON.stringify(se_ListMessageMoveTasksRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListMessageMoveTasksCommand");
var se_ListQueuesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListQueues");
  let body;
  body = JSON.stringify(se_ListQueuesRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListQueuesCommand");
var se_ListQueueTagsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListQueueTags");
  let body;
  body = JSON.stringify(se_ListQueueTagsRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListQueueTagsCommand");
var se_PurgeQueueCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("PurgeQueue");
  let body;
  body = JSON.stringify(se_PurgeQueueRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_PurgeQueueCommand");
var se_ReceiveMessageCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ReceiveMessage");
  let body;
  body = JSON.stringify(se_ReceiveMessageRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ReceiveMessageCommand");
var se_RemovePermissionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("RemovePermission");
  let body;
  body = JSON.stringify(se_RemovePermissionRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_RemovePermissionCommand");
var se_SendMessageCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SendMessage");
  let body;
  body = JSON.stringify(se_SendMessageRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SendMessageCommand");
var se_SendMessageBatchCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SendMessageBatch");
  let body;
  body = JSON.stringify(se_SendMessageBatchRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SendMessageBatchCommand");
var se_SetQueueAttributesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("SetQueueAttributes");
  let body;
  body = JSON.stringify(se_SetQueueAttributesRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_SetQueueAttributesCommand");
var se_StartMessageMoveTaskCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("StartMessageMoveTask");
  let body;
  body = JSON.stringify(se_StartMessageMoveTaskRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_StartMessageMoveTaskCommand");
var se_TagQueueCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("TagQueue");
  let body;
  body = JSON.stringify(se_TagQueueRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_TagQueueCommand");
var se_UntagQueueCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UntagQueue");
  let body;
  body = JSON.stringify(se_UntagQueueRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UntagQueueCommand");
var de_AddPermissionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_AddPermissionCommand");
var de_CancelMessageMoveTaskCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CancelMessageMoveTaskCommand");
var de_ChangeMessageVisibilityCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_ChangeMessageVisibilityCommand");
var de_ChangeMessageVisibilityBatchCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ChangeMessageVisibilityBatchCommand");
var de_CreateQueueCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateQueueCommand");
var de_DeleteMessageCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteMessageCommand");
var de_DeleteMessageBatchCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DeleteMessageBatchCommand");
var de_DeleteQueueCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_DeleteQueueCommand");
var de_GetQueueAttributesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetQueueAttributesCommand");
var de_GetQueueUrlCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetQueueUrlCommand");
var de_ListDeadLetterSourceQueuesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListDeadLetterSourceQueuesCommand");
var de_ListMessageMoveTasksCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListMessageMoveTasksCommand");
var de_ListQueuesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListQueuesCommand");
var de_ListQueueTagsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListQueueTagsCommand");
var de_PurgeQueueCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_PurgeQueueCommand");
var de_ReceiveMessageCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ReceiveMessageResult(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ReceiveMessageCommand");
var de_RemovePermissionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_RemovePermissionCommand");
var de_SendMessageCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SendMessageCommand");
var de_SendMessageBatchCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_SendMessageBatchCommand");
var de_SetQueueAttributesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_SetQueueAttributesCommand");
var de_StartMessageMoveTaskCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_StartMessageMoveTaskCommand");
var de_TagQueueCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_TagQueueCommand");
var de_UntagQueueCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  await (0, import_smithy_client.collectBody)(output.body, context);
  const response = {
    $metadata: deserializeMetadata(output)
  };
  return response;
}, "de_UntagQueueCommand");
var de_CommandError = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonErrorBody)(output.body, context)
  };
  populateBodyWithQueryCompatibility(parsedOutput, output.headers);
  const errorCode = (0, import_core2.loadRestJsonErrorCode)(output, parsedOutput.body);
  switch (errorCode) {
    case "InvalidAddress":
    case "com.amazonaws.sqs#InvalidAddress":
      throw await de_InvalidAddressRes(parsedOutput, context);
    case "InvalidSecurity":
    case "com.amazonaws.sqs#InvalidSecurity":
      throw await de_InvalidSecurityRes(parsedOutput, context);
    case "OverLimit":
    case "com.amazonaws.sqs#OverLimit":
      throw await de_OverLimitRes(parsedOutput, context);
    case "QueueDoesNotExist":
    case "com.amazonaws.sqs#QueueDoesNotExist":
    case "AWS.SimpleQueueService.NonExistentQueue":
      throw await de_QueueDoesNotExistRes(parsedOutput, context);
    case "RequestThrottled":
    case "com.amazonaws.sqs#RequestThrottled":
      throw await de_RequestThrottledRes(parsedOutput, context);
    case "UnsupportedOperation":
    case "com.amazonaws.sqs#UnsupportedOperation":
    case "AWS.SimpleQueueService.UnsupportedOperation":
      throw await de_UnsupportedOperationRes(parsedOutput, context);
    case "ResourceNotFoundException":
    case "com.amazonaws.sqs#ResourceNotFoundException":
      throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);
    case "MessageNotInflight":
    case "com.amazonaws.sqs#MessageNotInflight":
    case "AWS.SimpleQueueService.MessageNotInflight":
      throw await de_MessageNotInflightRes(parsedOutput, context);
    case "ReceiptHandleIsInvalid":
    case "com.amazonaws.sqs#ReceiptHandleIsInvalid":
      throw await de_ReceiptHandleIsInvalidRes(parsedOutput, context);
    case "BatchEntryIdsNotDistinct":
    case "com.amazonaws.sqs#BatchEntryIdsNotDistinct":
    case "AWS.SimpleQueueService.BatchEntryIdsNotDistinct":
      throw await de_BatchEntryIdsNotDistinctRes(parsedOutput, context);
    case "EmptyBatchRequest":
    case "com.amazonaws.sqs#EmptyBatchRequest":
    case "AWS.SimpleQueueService.EmptyBatchRequest":
      throw await de_EmptyBatchRequestRes(parsedOutput, context);
    case "InvalidBatchEntryId":
    case "com.amazonaws.sqs#InvalidBatchEntryId":
    case "AWS.SimpleQueueService.InvalidBatchEntryId":
      throw await de_InvalidBatchEntryIdRes(parsedOutput, context);
    case "TooManyEntriesInBatchRequest":
    case "com.amazonaws.sqs#TooManyEntriesInBatchRequest":
    case "AWS.SimpleQueueService.TooManyEntriesInBatchRequest":
      throw await de_TooManyEntriesInBatchRequestRes(parsedOutput, context);
    case "InvalidAttributeName":
    case "com.amazonaws.sqs#InvalidAttributeName":
      throw await de_InvalidAttributeNameRes(parsedOutput, context);
    case "InvalidAttributeValue":
    case "com.amazonaws.sqs#InvalidAttributeValue":
      throw await de_InvalidAttributeValueRes(parsedOutput, context);
    case "QueueDeletedRecently":
    case "com.amazonaws.sqs#QueueDeletedRecently":
    case "AWS.SimpleQueueService.QueueDeletedRecently":
      throw await de_QueueDeletedRecentlyRes(parsedOutput, context);
    case "QueueNameExists":
    case "com.amazonaws.sqs#QueueNameExists":
    case "QueueAlreadyExists":
      throw await de_QueueNameExistsRes(parsedOutput, context);
    case "InvalidIdFormat":
    case "com.amazonaws.sqs#InvalidIdFormat":
      throw await de_InvalidIdFormatRes(parsedOutput, context);
    case "PurgeQueueInProgress":
    case "com.amazonaws.sqs#PurgeQueueInProgress":
    case "AWS.SimpleQueueService.PurgeQueueInProgress":
      throw await de_PurgeQueueInProgressRes(parsedOutput, context);
    case "KmsAccessDenied":
    case "com.amazonaws.sqs#KmsAccessDenied":
    case "KMS.AccessDeniedException":
      throw await de_KmsAccessDeniedRes(parsedOutput, context);
    case "KmsDisabled":
    case "com.amazonaws.sqs#KmsDisabled":
    case "KMS.DisabledException":
      throw await de_KmsDisabledRes(parsedOutput, context);
    case "KmsInvalidKeyUsage":
    case "com.amazonaws.sqs#KmsInvalidKeyUsage":
    case "KMS.InvalidKeyUsageException":
      throw await de_KmsInvalidKeyUsageRes(parsedOutput, context);
    case "KmsInvalidState":
    case "com.amazonaws.sqs#KmsInvalidState":
    case "KMS.InvalidStateException":
      throw await de_KmsInvalidStateRes(parsedOutput, context);
    case "KmsNotFound":
    case "com.amazonaws.sqs#KmsNotFound":
    case "KMS.NotFoundException":
      throw await de_KmsNotFoundRes(parsedOutput, context);
    case "KmsOptInRequired":
    case "com.amazonaws.sqs#KmsOptInRequired":
    case "KMS.OptInRequired":
      throw await de_KmsOptInRequiredRes(parsedOutput, context);
    case "KmsThrottled":
    case "com.amazonaws.sqs#KmsThrottled":
    case "KMS.ThrottlingException":
      throw await de_KmsThrottledRes(parsedOutput, context);
    case "InvalidMessageContents":
    case "com.amazonaws.sqs#InvalidMessageContents":
      throw await de_InvalidMessageContentsRes(parsedOutput, context);
    case "BatchRequestTooLong":
    case "com.amazonaws.sqs#BatchRequestTooLong":
    case "AWS.SimpleQueueService.BatchRequestTooLong":
      throw await de_BatchRequestTooLongRes(parsedOutput, context);
    default:
      const parsedBody = parsedOutput.body;
      return throwDefaultError({
        output,
        parsedBody,
        errorCode
      });
  }
}, "de_CommandError");
var de_BatchEntryIdsNotDistinctRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new BatchEntryIdsNotDistinct({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_BatchEntryIdsNotDistinctRes");
var de_BatchRequestTooLongRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new BatchRequestTooLong({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_BatchRequestTooLongRes");
var de_EmptyBatchRequestRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new EmptyBatchRequest({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_EmptyBatchRequestRes");
var de_InvalidAddressRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidAddress({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidAddressRes");
var de_InvalidAttributeNameRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidAttributeName({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidAttributeNameRes");
var de_InvalidAttributeValueRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidAttributeValue({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidAttributeValueRes");
var de_InvalidBatchEntryIdRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidBatchEntryId({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidBatchEntryIdRes");
var de_InvalidIdFormatRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidIdFormat({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidIdFormatRes");
var de_InvalidMessageContentsRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidMessageContents({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidMessageContentsRes");
var de_InvalidSecurityRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidSecurity({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidSecurityRes");
var de_KmsAccessDeniedRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new KmsAccessDenied({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_KmsAccessDeniedRes");
var de_KmsDisabledRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new KmsDisabled({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_KmsDisabledRes");
var de_KmsInvalidKeyUsageRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new KmsInvalidKeyUsage({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_KmsInvalidKeyUsageRes");
var de_KmsInvalidStateRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new KmsInvalidState({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_KmsInvalidStateRes");
var de_KmsNotFoundRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new KmsNotFound({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_KmsNotFoundRes");
var de_KmsOptInRequiredRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new KmsOptInRequired({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_KmsOptInRequiredRes");
var de_KmsThrottledRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new KmsThrottled({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_KmsThrottledRes");
var de_MessageNotInflightRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new MessageNotInflight({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_MessageNotInflightRes");
var de_OverLimitRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new OverLimit({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_OverLimitRes");
var de_PurgeQueueInProgressRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new PurgeQueueInProgress({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_PurgeQueueInProgressRes");
var de_QueueDeletedRecentlyRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new QueueDeletedRecently({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_QueueDeletedRecentlyRes");
var de_QueueDoesNotExistRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new QueueDoesNotExist({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_QueueDoesNotExistRes");
var de_QueueNameExistsRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new QueueNameExists({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_QueueNameExistsRes");
var de_ReceiptHandleIsInvalidRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ReceiptHandleIsInvalid({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ReceiptHandleIsInvalidRes");
var de_RequestThrottledRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new RequestThrottled({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_RequestThrottledRes");
var de_ResourceNotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ResourceNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ResourceNotFoundExceptionRes");
var de_TooManyEntriesInBatchRequestRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new TooManyEntriesInBatchRequest({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_TooManyEntriesInBatchRequestRes");
var de_UnsupportedOperationRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UnsupportedOperation({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UnsupportedOperationRes");
var se_ActionNameList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return (0, import_core2._toStr)(entry);
  });
}, "se_ActionNameList");
var se_AddPermissionRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AWSAccountIds: /* @__PURE__ */ __name((_) => se_AWSAccountIdList(_, context), "AWSAccountIds"),
    Actions: /* @__PURE__ */ __name((_) => se_ActionNameList(_, context), "Actions"),
    Label: import_core2._toStr,
    QueueUrl: import_core2._toStr
  });
}, "se_AddPermissionRequest");
var se_AttributeNameList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return (0, import_core2._toStr)(entry);
  });
}, "se_AttributeNameList");
var se_AWSAccountIdList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return (0, import_core2._toStr)(entry);
  });
}, "se_AWSAccountIdList");
var se_BinaryList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return context.base64Encoder(entry);
  });
}, "se_BinaryList");
var se_CancelMessageMoveTaskRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    TaskHandle: import_core2._toStr
  });
}, "se_CancelMessageMoveTaskRequest");
var se_ChangeMessageVisibilityBatchRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Entries: /* @__PURE__ */ __name((_) => se_ChangeMessageVisibilityBatchRequestEntryList(_, context), "Entries"),
    QueueUrl: import_core2._toStr
  });
}, "se_ChangeMessageVisibilityBatchRequest");
var se_ChangeMessageVisibilityBatchRequestEntry = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Id: import_core2._toStr,
    ReceiptHandle: import_core2._toStr,
    VisibilityTimeout: import_core2._toNum
  });
}, "se_ChangeMessageVisibilityBatchRequestEntry");
var se_ChangeMessageVisibilityBatchRequestEntryList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_ChangeMessageVisibilityBatchRequestEntry(entry, context);
  });
}, "se_ChangeMessageVisibilityBatchRequestEntryList");
var se_ChangeMessageVisibilityRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    QueueUrl: import_core2._toStr,
    ReceiptHandle: import_core2._toStr,
    VisibilityTimeout: import_core2._toNum
  });
}, "se_ChangeMessageVisibilityRequest");
var se_CreateQueueRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Attributes: /* @__PURE__ */ __name((_) => se_QueueAttributeMap(_, context), "Attributes"),
    QueueName: import_core2._toStr,
    tags: /* @__PURE__ */ __name((_) => se_TagMap(_, context), "tags")
  });
}, "se_CreateQueueRequest");
var se_DeleteMessageBatchRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Entries: /* @__PURE__ */ __name((_) => se_DeleteMessageBatchRequestEntryList(_, context), "Entries"),
    QueueUrl: import_core2._toStr
  });
}, "se_DeleteMessageBatchRequest");
var se_DeleteMessageBatchRequestEntry = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Id: import_core2._toStr,
    ReceiptHandle: import_core2._toStr
  });
}, "se_DeleteMessageBatchRequestEntry");
var se_DeleteMessageBatchRequestEntryList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_DeleteMessageBatchRequestEntry(entry, context);
  });
}, "se_DeleteMessageBatchRequestEntryList");
var se_DeleteMessageRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    QueueUrl: import_core2._toStr,
    ReceiptHandle: import_core2._toStr
  });
}, "se_DeleteMessageRequest");
var se_DeleteQueueRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    QueueUrl: import_core2._toStr
  });
}, "se_DeleteQueueRequest");
var se_GetQueueAttributesRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AttributeNames: /* @__PURE__ */ __name((_) => se_AttributeNameList(_, context), "AttributeNames"),
    QueueUrl: import_core2._toStr
  });
}, "se_GetQueueAttributesRequest");
var se_GetQueueUrlRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    QueueName: import_core2._toStr,
    QueueOwnerAWSAccountId: import_core2._toStr
  });
}, "se_GetQueueUrlRequest");
var se_ListDeadLetterSourceQueuesRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    MaxResults: import_core2._toNum,
    NextToken: import_core2._toStr,
    QueueUrl: import_core2._toStr
  });
}, "se_ListDeadLetterSourceQueuesRequest");
var se_ListMessageMoveTasksRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    MaxResults: import_core2._toNum,
    SourceArn: import_core2._toStr
  });
}, "se_ListMessageMoveTasksRequest");
var se_ListQueuesRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    MaxResults: import_core2._toNum,
    NextToken: import_core2._toStr,
    QueueNamePrefix: import_core2._toStr
  });
}, "se_ListQueuesRequest");
var se_ListQueueTagsRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    QueueUrl: import_core2._toStr
  });
}, "se_ListQueueTagsRequest");
var se_MessageAttributeNameList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return (0, import_core2._toStr)(entry);
  });
}, "se_MessageAttributeNameList");
var se_MessageAttributeValue = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    BinaryListValues: /* @__PURE__ */ __name((_) => se_BinaryList(_, context), "BinaryListValues"),
    BinaryValue: context.base64Encoder,
    DataType: import_core2._toStr,
    StringListValues: /* @__PURE__ */ __name((_) => se_StringList(_, context), "StringListValues"),
    StringValue: import_core2._toStr
  });
}, "se_MessageAttributeValue");
var se_MessageBodyAttributeMap = /* @__PURE__ */ __name((input, context) => {
  return Object.entries(input).reduce((acc, [key, value]) => {
    if (value === null) {
      return acc;
    }
    acc[key] = se_MessageAttributeValue(value, context);
    return acc;
  }, {});
}, "se_MessageBodyAttributeMap");
var se_MessageBodySystemAttributeMap = /* @__PURE__ */ __name((input, context) => {
  return Object.entries(input).reduce(
    (acc, [key, value]) => {
      if (value === null) {
        return acc;
      }
      acc[key] = se_MessageSystemAttributeValue(value, context);
      return acc;
    },
    {}
  );
}, "se_MessageBodySystemAttributeMap");
var se_MessageSystemAttributeList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return (0, import_core2._toStr)(entry);
  });
}, "se_MessageSystemAttributeList");
var se_MessageSystemAttributeValue = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    BinaryListValues: /* @__PURE__ */ __name((_) => se_BinaryList(_, context), "BinaryListValues"),
    BinaryValue: context.base64Encoder,
    DataType: import_core2._toStr,
    StringListValues: /* @__PURE__ */ __name((_) => se_StringList(_, context), "StringListValues"),
    StringValue: import_core2._toStr
  });
}, "se_MessageSystemAttributeValue");
var se_PurgeQueueRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    QueueUrl: import_core2._toStr
  });
}, "se_PurgeQueueRequest");
var se_QueueAttributeMap = /* @__PURE__ */ __name((input, context) => {
  return Object.entries(input).reduce((acc, [key, value]) => {
    if (value === null) {
      return acc;
    }
    acc[key] = (0, import_core2._toStr)(value);
    return acc;
  }, {});
}, "se_QueueAttributeMap");
var se_ReceiveMessageRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AttributeNames: /* @__PURE__ */ __name((_) => se_AttributeNameList(_, context), "AttributeNames"),
    MaxNumberOfMessages: import_core2._toNum,
    MessageAttributeNames: /* @__PURE__ */ __name((_) => se_MessageAttributeNameList(_, context), "MessageAttributeNames"),
    MessageSystemAttributeNames: /* @__PURE__ */ __name((_) => se_MessageSystemAttributeList(_, context), "MessageSystemAttributeNames"),
    QueueUrl: import_core2._toStr,
    ReceiveRequestAttemptId: import_core2._toStr,
    VisibilityTimeout: import_core2._toNum,
    WaitTimeSeconds: import_core2._toNum
  });
}, "se_ReceiveMessageRequest");
var se_RemovePermissionRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Label: import_core2._toStr,
    QueueUrl: import_core2._toStr
  });
}, "se_RemovePermissionRequest");
var se_SendMessageBatchRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Entries: /* @__PURE__ */ __name((_) => se_SendMessageBatchRequestEntryList(_, context), "Entries"),
    QueueUrl: import_core2._toStr
  });
}, "se_SendMessageBatchRequest");
var se_SendMessageBatchRequestEntry = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    DelaySeconds: import_core2._toNum,
    Id: import_core2._toStr,
    MessageAttributes: /* @__PURE__ */ __name((_) => se_MessageBodyAttributeMap(_, context), "MessageAttributes"),
    MessageBody: import_core2._toStr,
    MessageDeduplicationId: import_core2._toStr,
    MessageGroupId: import_core2._toStr,
    MessageSystemAttributes: /* @__PURE__ */ __name((_) => se_MessageBodySystemAttributeMap(_, context), "MessageSystemAttributes")
  });
}, "se_SendMessageBatchRequestEntry");
var se_SendMessageBatchRequestEntryList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_SendMessageBatchRequestEntry(entry, context);
  });
}, "se_SendMessageBatchRequestEntryList");
var se_SendMessageRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    DelaySeconds: import_core2._toNum,
    MessageAttributes: /* @__PURE__ */ __name((_) => se_MessageBodyAttributeMap(_, context), "MessageAttributes"),
    MessageBody: import_core2._toStr,
    MessageDeduplicationId: import_core2._toStr,
    MessageGroupId: import_core2._toStr,
    MessageSystemAttributes: /* @__PURE__ */ __name((_) => se_MessageBodySystemAttributeMap(_, context), "MessageSystemAttributes"),
    QueueUrl: import_core2._toStr
  });
}, "se_SendMessageRequest");
var se_SetQueueAttributesRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Attributes: /* @__PURE__ */ __name((_) => se_QueueAttributeMap(_, context), "Attributes"),
    QueueUrl: import_core2._toStr
  });
}, "se_SetQueueAttributesRequest");
var se_StartMessageMoveTaskRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    DestinationArn: import_core2._toStr,
    MaxNumberOfMessagesPerSecond: import_core2._toNum,
    SourceArn: import_core2._toStr
  });
}, "se_StartMessageMoveTaskRequest");
var se_StringList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return (0, import_core2._toStr)(entry);
  });
}, "se_StringList");
var se_TagKeyList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return (0, import_core2._toStr)(entry);
  });
}, "se_TagKeyList");
var se_TagMap = /* @__PURE__ */ __name((input, context) => {
  return Object.entries(input).reduce((acc, [key, value]) => {
    if (value === null) {
      return acc;
    }
    acc[key] = (0, import_core2._toStr)(value);
    return acc;
  }, {});
}, "se_TagMap");
var se_TagQueueRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    QueueUrl: import_core2._toStr,
    Tags: /* @__PURE__ */ __name((_) => se_TagMap(_, context), "Tags")
  });
}, "se_TagQueueRequest");
var se_UntagQueueRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    QueueUrl: import_core2._toStr,
    TagKeys: /* @__PURE__ */ __name((_) => se_TagKeyList(_, context), "TagKeys")
  });
}, "se_UntagQueueRequest");
var de_BinaryList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return context.base64Decoder(entry);
  });
  return retVal;
}, "de_BinaryList");
var de_Message = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Attributes: import_smithy_client._json,
    Body: import_smithy_client.expectString,
    MD5OfBody: import_smithy_client.expectString,
    MD5OfMessageAttributes: import_smithy_client.expectString,
    MessageAttributes: /* @__PURE__ */ __name((_) => de_MessageBodyAttributeMap(_, context), "MessageAttributes"),
    MessageId: import_smithy_client.expectString,
    ReceiptHandle: import_smithy_client.expectString
  });
}, "de_Message");
var de_MessageAttributeValue = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    BinaryListValues: /* @__PURE__ */ __name((_) => de_BinaryList(_, context), "BinaryListValues"),
    BinaryValue: context.base64Decoder,
    DataType: import_smithy_client.expectString,
    StringListValues: import_smithy_client._json,
    StringValue: import_smithy_client.expectString
  });
}, "de_MessageAttributeValue");
var de_MessageBodyAttributeMap = /* @__PURE__ */ __name((output, context) => {
  return Object.entries(output).reduce((acc, [key, value]) => {
    if (value === null) {
      return acc;
    }
    acc[key] = de_MessageAttributeValue(value, context);
    return acc;
  }, {});
}, "de_MessageBodyAttributeMap");
var de_MessageList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Message(entry, context);
  });
  return retVal;
}, "de_MessageList");
var de_ReceiveMessageResult = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Messages: /* @__PURE__ */ __name((_) => de_MessageList(_, context), "Messages")
  });
}, "de_ReceiveMessageResult");
var deserializeMetadata = /* @__PURE__ */ __name((output) => ({
  httpStatusCode: output.statusCode,
  requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
  extendedRequestId: output.headers["x-amz-id-2"],
  cfId: output.headers["x-amz-cf-id"]
}), "deserializeMetadata");
var throwDefaultError = (0, import_smithy_client.withBaseException)(SQSServiceException);
var buildHttpRpcRequest = /* @__PURE__ */ __name(async (context, headers, path, resolvedHostname, body) => {
  const { hostname, protocol = "https", port, path: basePath } = await context.endpoint();
  const contents = {
    protocol,
    hostname,
    port,
    method: "POST",
    path: basePath.endsWith("/") ? basePath.slice(0, -1) + path : basePath + path,
    headers
  };
  if (resolvedHostname !== void 0) {
    contents.hostname = resolvedHostname;
  }
  if (body !== void 0) {
    contents.body = body;
  }
  return new import_protocol_http.HttpRequest(contents);
}, "buildHttpRpcRequest");
function sharedHeaders(operation) {
  return {
    "content-type": "application/x-amz-json-1.0",
    "x-amz-target": `AmazonSQS.${operation}`,
    "x-amzn-query-mode": "true"
  };
}
__name(sharedHeaders, "sharedHeaders");
var populateBodyWithQueryCompatibility = /* @__PURE__ */ __name((parsedOutput, headers) => {
  const queryErrorHeader = headers["x-amzn-query-error"];
  if (parsedOutput.body !== void 0 && queryErrorHeader != null) {
    const [Code, Type] = queryErrorHeader.split(";");
    const entries = Object.entries(parsedOutput.body);
    const Error2 = {
      Type,
      Code
    };
    Object.assign(parsedOutput.body, Error2);
    for (const [k, v] of entries) {
      Error2[k] = v;
    }
    delete Error2.__type;
    parsedOutput.body.Error = Error2;
  }
}, "populateBodyWithQueryCompatibility");

// src/commands/AddPermissionCommand.ts
var AddPermissionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "AddPermission", {}).n("SQSClient", "AddPermissionCommand").f(void 0, void 0).ser(se_AddPermissionCommand).de(de_AddPermissionCommand).build() {
  static {
    __name(this, "AddPermissionCommand");
  }
};

// src/commands/CancelMessageMoveTaskCommand.ts



var CancelMessageMoveTaskCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "CancelMessageMoveTask", {}).n("SQSClient", "CancelMessageMoveTaskCommand").f(void 0, void 0).ser(se_CancelMessageMoveTaskCommand).de(de_CancelMessageMoveTaskCommand).build() {
  static {
    __name(this, "CancelMessageMoveTaskCommand");
  }
};

// src/commands/ChangeMessageVisibilityBatchCommand.ts



var ChangeMessageVisibilityBatchCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "ChangeMessageVisibilityBatch", {}).n("SQSClient", "ChangeMessageVisibilityBatchCommand").f(void 0, void 0).ser(se_ChangeMessageVisibilityBatchCommand).de(de_ChangeMessageVisibilityBatchCommand).build() {
  static {
    __name(this, "ChangeMessageVisibilityBatchCommand");
  }
};

// src/commands/ChangeMessageVisibilityCommand.ts



var ChangeMessageVisibilityCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "ChangeMessageVisibility", {}).n("SQSClient", "ChangeMessageVisibilityCommand").f(void 0, void 0).ser(se_ChangeMessageVisibilityCommand).de(de_ChangeMessageVisibilityCommand).build() {
  static {
    __name(this, "ChangeMessageVisibilityCommand");
  }
};

// src/commands/CreateQueueCommand.ts



var CreateQueueCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "CreateQueue", {}).n("SQSClient", "CreateQueueCommand").f(void 0, void 0).ser(se_CreateQueueCommand).de(de_CreateQueueCommand).build() {
  static {
    __name(this, "CreateQueueCommand");
  }
};

// src/commands/DeleteMessageBatchCommand.ts



var DeleteMessageBatchCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "DeleteMessageBatch", {}).n("SQSClient", "DeleteMessageBatchCommand").f(void 0, void 0).ser(se_DeleteMessageBatchCommand).de(de_DeleteMessageBatchCommand).build() {
  static {
    __name(this, "DeleteMessageBatchCommand");
  }
};

// src/commands/DeleteMessageCommand.ts



var DeleteMessageCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "DeleteMessage", {}).n("SQSClient", "DeleteMessageCommand").f(void 0, void 0).ser(se_DeleteMessageCommand).de(de_DeleteMessageCommand).build() {
  static {
    __name(this, "DeleteMessageCommand");
  }
};

// src/commands/DeleteQueueCommand.ts



var DeleteQueueCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "DeleteQueue", {}).n("SQSClient", "DeleteQueueCommand").f(void 0, void 0).ser(se_DeleteQueueCommand).de(de_DeleteQueueCommand).build() {
  static {
    __name(this, "DeleteQueueCommand");
  }
};

// src/commands/GetQueueAttributesCommand.ts



var GetQueueAttributesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "GetQueueAttributes", {}).n("SQSClient", "GetQueueAttributesCommand").f(void 0, void 0).ser(se_GetQueueAttributesCommand).de(de_GetQueueAttributesCommand).build() {
  static {
    __name(this, "GetQueueAttributesCommand");
  }
};

// src/commands/GetQueueUrlCommand.ts



var GetQueueUrlCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "GetQueueUrl", {}).n("SQSClient", "GetQueueUrlCommand").f(void 0, void 0).ser(se_GetQueueUrlCommand).de(de_GetQueueUrlCommand).build() {
  static {
    __name(this, "GetQueueUrlCommand");
  }
};

// src/commands/ListDeadLetterSourceQueuesCommand.ts



var ListDeadLetterSourceQueuesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "ListDeadLetterSourceQueues", {}).n("SQSClient", "ListDeadLetterSourceQueuesCommand").f(void 0, void 0).ser(se_ListDeadLetterSourceQueuesCommand).de(de_ListDeadLetterSourceQueuesCommand).build() {
  static {
    __name(this, "ListDeadLetterSourceQueuesCommand");
  }
};

// src/commands/ListMessageMoveTasksCommand.ts



var ListMessageMoveTasksCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "ListMessageMoveTasks", {}).n("SQSClient", "ListMessageMoveTasksCommand").f(void 0, void 0).ser(se_ListMessageMoveTasksCommand).de(de_ListMessageMoveTasksCommand).build() {
  static {
    __name(this, "ListMessageMoveTasksCommand");
  }
};

// src/commands/ListQueuesCommand.ts



var ListQueuesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "ListQueues", {}).n("SQSClient", "ListQueuesCommand").f(void 0, void 0).ser(se_ListQueuesCommand).de(de_ListQueuesCommand).build() {
  static {
    __name(this, "ListQueuesCommand");
  }
};

// src/commands/ListQueueTagsCommand.ts



var ListQueueTagsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "ListQueueTags", {}).n("SQSClient", "ListQueueTagsCommand").f(void 0, void 0).ser(se_ListQueueTagsCommand).de(de_ListQueueTagsCommand).build() {
  static {
    __name(this, "ListQueueTagsCommand");
  }
};

// src/commands/PurgeQueueCommand.ts



var PurgeQueueCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "PurgeQueue", {}).n("SQSClient", "PurgeQueueCommand").f(void 0, void 0).ser(se_PurgeQueueCommand).de(de_PurgeQueueCommand).build() {
  static {
    __name(this, "PurgeQueueCommand");
  }
};

// src/commands/ReceiveMessageCommand.ts




var ReceiveMessageCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_sqs.getReceiveMessagePlugin)(config)
  ];
}).s("AmazonSQS", "ReceiveMessage", {}).n("SQSClient", "ReceiveMessageCommand").f(void 0, void 0).ser(se_ReceiveMessageCommand).de(de_ReceiveMessageCommand).build() {
  static {
    __name(this, "ReceiveMessageCommand");
  }
};

// src/commands/RemovePermissionCommand.ts



var RemovePermissionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "RemovePermission", {}).n("SQSClient", "RemovePermissionCommand").f(void 0, void 0).ser(se_RemovePermissionCommand).de(de_RemovePermissionCommand).build() {
  static {
    __name(this, "RemovePermissionCommand");
  }
};

// src/commands/SendMessageBatchCommand.ts




var SendMessageBatchCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_sqs.getSendMessageBatchPlugin)(config)
  ];
}).s("AmazonSQS", "SendMessageBatch", {}).n("SQSClient", "SendMessageBatchCommand").f(void 0, void 0).ser(se_SendMessageBatchCommand).de(de_SendMessageBatchCommand).build() {
  static {
    __name(this, "SendMessageBatchCommand");
  }
};

// src/commands/SendMessageCommand.ts




var SendMessageCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_sqs.getSendMessagePlugin)(config)
  ];
}).s("AmazonSQS", "SendMessage", {}).n("SQSClient", "SendMessageCommand").f(void 0, void 0).ser(se_SendMessageCommand).de(de_SendMessageCommand).build() {
  static {
    __name(this, "SendMessageCommand");
  }
};

// src/commands/SetQueueAttributesCommand.ts



var SetQueueAttributesCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "SetQueueAttributes", {}).n("SQSClient", "SetQueueAttributesCommand").f(void 0, void 0).ser(se_SetQueueAttributesCommand).de(de_SetQueueAttributesCommand).build() {
  static {
    __name(this, "SetQueueAttributesCommand");
  }
};

// src/commands/StartMessageMoveTaskCommand.ts



var StartMessageMoveTaskCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "StartMessageMoveTask", {}).n("SQSClient", "StartMessageMoveTaskCommand").f(void 0, void 0).ser(se_StartMessageMoveTaskCommand).de(de_StartMessageMoveTaskCommand).build() {
  static {
    __name(this, "StartMessageMoveTaskCommand");
  }
};

// src/commands/TagQueueCommand.ts



var TagQueueCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "TagQueue", {}).n("SQSClient", "TagQueueCommand").f(void 0, void 0).ser(se_TagQueueCommand).de(de_TagQueueCommand).build() {
  static {
    __name(this, "TagQueueCommand");
  }
};

// src/commands/UntagQueueCommand.ts



var UntagQueueCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonSQS", "UntagQueue", {}).n("SQSClient", "UntagQueueCommand").f(void 0, void 0).ser(se_UntagQueueCommand).de(de_UntagQueueCommand).build() {
  static {
    __name(this, "UntagQueueCommand");
  }
};

// src/SQS.ts
var commands = {
  AddPermissionCommand,
  CancelMessageMoveTaskCommand,
  ChangeMessageVisibilityCommand,
  ChangeMessageVisibilityBatchCommand,
  CreateQueueCommand,
  DeleteMessageCommand,
  DeleteMessageBatchCommand,
  DeleteQueueCommand,
  GetQueueAttributesCommand,
  GetQueueUrlCommand,
  ListDeadLetterSourceQueuesCommand,
  ListMessageMoveTasksCommand,
  ListQueuesCommand,
  ListQueueTagsCommand,
  PurgeQueueCommand,
  ReceiveMessageCommand,
  RemovePermissionCommand,
  SendMessageCommand,
  SendMessageBatchCommand,
  SetQueueAttributesCommand,
  StartMessageMoveTaskCommand,
  TagQueueCommand,
  UntagQueueCommand
};
var SQS = class extends SQSClient {
  static {
    __name(this, "SQS");
  }
};
(0, import_smithy_client.createAggregatedClient)(commands, SQS);

// src/pagination/ListDeadLetterSourceQueuesPaginator.ts

var paginateListDeadLetterSourceQueues = (0, import_core.createPaginator)(SQSClient, ListDeadLetterSourceQueuesCommand, "NextToken", "NextToken", "MaxResults");

// src/pagination/ListQueuesPaginator.ts

var paginateListQueues = (0, import_core.createPaginator)(SQSClient, ListQueuesCommand, "NextToken", "NextToken", "MaxResults");
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  SQSServiceException,
  __Client,
  SQSClient,
  SQS,
  $Command,
  AddPermissionCommand,
  CancelMessageMoveTaskCommand,
  ChangeMessageVisibilityBatchCommand,
  ChangeMessageVisibilityCommand,
  CreateQueueCommand,
  DeleteMessageBatchCommand,
  DeleteMessageCommand,
  DeleteQueueCommand,
  GetQueueAttributesCommand,
  GetQueueUrlCommand,
  ListDeadLetterSourceQueuesCommand,
  ListMessageMoveTasksCommand,
  ListQueueTagsCommand,
  ListQueuesCommand,
  PurgeQueueCommand,
  ReceiveMessageCommand,
  RemovePermissionCommand,
  SendMessageBatchCommand,
  SendMessageCommand,
  SetQueueAttributesCommand,
  StartMessageMoveTaskCommand,
  TagQueueCommand,
  UntagQueueCommand,
  paginateListDeadLetterSourceQueues,
  paginateListQueues,
  InvalidAddress,
  InvalidSecurity,
  OverLimit,
  QueueDoesNotExist,
  RequestThrottled,
  UnsupportedOperation,
  ResourceNotFoundException,
  MessageNotInflight,
  ReceiptHandleIsInvalid,
  BatchEntryIdsNotDistinct,
  EmptyBatchRequest,
  InvalidBatchEntryId,
  TooManyEntriesInBatchRequest,
  QueueAttributeName,
  InvalidAttributeName,
  InvalidAttributeValue,
  QueueDeletedRecently,
  QueueNameExists,
  InvalidIdFormat,
  PurgeQueueInProgress,
  KmsAccessDenied,
  KmsDisabled,
  KmsInvalidKeyUsage,
  KmsInvalidState,
  KmsNotFound,
  KmsOptInRequired,
  KmsThrottled,
  MessageSystemAttributeName,
  InvalidMessageContents,
  MessageSystemAttributeNameForSends,
  BatchRequestTooLong
});

