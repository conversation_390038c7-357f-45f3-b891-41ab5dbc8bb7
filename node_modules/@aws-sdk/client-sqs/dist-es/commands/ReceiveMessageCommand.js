import { getReceiveMessagePlugin } from "@aws-sdk/middleware-sdk-sqs";
import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_ReceiveMessageCommand, se_ReceiveMessageCommand } from "../protocols/Aws_json1_0";
export { $Command };
export class ReceiveMessageCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
        getReceiveMessagePlugin(config),
    ];
})
    .s("AmazonSQS", "ReceiveMessage", {})
    .n("SQSClient", "ReceiveMessageCommand")
    .f(void 0, void 0)
    .ser(se_ReceiveMessageCommand)
    .de(de_ReceiveMessageCommand)
    .build() {
}
