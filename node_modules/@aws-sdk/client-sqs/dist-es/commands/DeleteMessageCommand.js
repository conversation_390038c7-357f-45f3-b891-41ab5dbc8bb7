import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_DeleteMessageCommand, se_DeleteMessageCommand } from "../protocols/Aws_json1_0";
export { $Command };
export class DeleteMessageCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSQS", "DeleteMessage", {})
    .n("SQSClient", "DeleteMessageCommand")
    .f(void 0, void 0)
    .ser(se_DeleteMessageCommand)
    .de(de_DeleteMessageCommand)
    .build() {
}
