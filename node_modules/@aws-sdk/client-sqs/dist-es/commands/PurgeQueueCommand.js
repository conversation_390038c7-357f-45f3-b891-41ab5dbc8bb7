import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_PurgeQueueCommand, se_PurgeQueueCommand } from "../protocols/Aws_json1_0";
export { $Command };
export class PurgeQueueCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSQS", "PurgeQueue", {})
    .n("SQSClient", "PurgeQueueCommand")
    .f(void 0, void 0)
    .ser(se_PurgeQueueCommand)
    .de(de_PurgeQueueCommand)
    .build() {
}
