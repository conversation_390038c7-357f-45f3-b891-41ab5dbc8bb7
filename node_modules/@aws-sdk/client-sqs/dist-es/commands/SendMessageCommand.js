import { getSendMessagePlugin } from "@aws-sdk/middleware-sdk-sqs";
import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_SendMessageCommand, se_SendMessageCommand } from "../protocols/Aws_json1_0";
export { $Command };
export class SendMessageCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
        getSendMessagePlugin(config),
    ];
})
    .s("AmazonSQS", "SendMessage", {})
    .n("SQSClient", "SendMessageCommand")
    .f(void 0, void 0)
    .ser(se_SendMessageCommand)
    .de(de_SendMessageCommand)
    .build() {
}
