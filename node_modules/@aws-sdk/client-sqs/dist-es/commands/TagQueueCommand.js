import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_TagQueueCommand, se_TagQueueCommand } from "../protocols/Aws_json1_0";
export { $Command };
export class TagQueueCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSQS", "TagQueue", {})
    .n("SQSClient", "TagQueueCommand")
    .f(void 0, void 0)
    .ser(se_TagQueueCommand)
    .de(de_TagQueueCommand)
    .build() {
}
