import { getSendMessageBatchPlugin } from "@aws-sdk/middleware-sdk-sqs";
import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_SendMessageBatchCommand, se_SendMessageBatchCommand } from "../protocols/Aws_json1_0";
export { $Command };
export class SendMessageBatchCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
        getSendMessageBatchPlugin(config),
    ];
})
    .s("AmazonSQS", "SendMessageBatch", {})
    .n("SQSClient", "SendMessageBatchCommand")
    .f(void 0, void 0)
    .ser(se_SendMessageBatchCommand)
    .de(de_SendMessageBatchCommand)
    .build() {
}
