import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_ListDeadLetterSourceQueuesCommand, se_ListDeadLetterSourceQueuesCommand } from "../protocols/Aws_json1_0";
export { $Command };
export class ListDeadLetterSourceQueuesCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSQS", "ListDeadLetterSourceQueues", {})
    .n("SQSClient", "ListDeadLetterSourceQueuesCommand")
    .f(void 0, void 0)
    .ser(se_ListDeadLetterSourceQueuesCommand)
    .de(de_ListDeadLetterSourceQueuesCommand)
    .build() {
}
