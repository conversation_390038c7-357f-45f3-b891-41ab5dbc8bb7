import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_GetQueueUrlCommand, se_GetQueueUrlCommand } from "../protocols/Aws_json1_0";
export { $Command };
export class GetQueueUrlCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSQS", "GetQueueUrl", {})
    .n("SQSClient", "GetQueueUrlCommand")
    .f(void 0, void 0)
    .ser(se_GetQueueUrlCommand)
    .de(de_GetQueueUrlCommand)
    .build() {
}
