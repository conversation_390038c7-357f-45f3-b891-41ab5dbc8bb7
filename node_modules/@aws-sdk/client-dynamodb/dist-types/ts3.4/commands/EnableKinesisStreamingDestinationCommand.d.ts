import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import {
  KinesisStreamingDestinationInput,
  KinesisStreamingDestinationOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface EnableKinesisStreamingDestinationCommandInput
  extends KinesisStreamingDestinationInput {}
export interface EnableKinesisStreamingDestinationCommandOutput
  extends KinesisStreamingDestinationOutput,
    __MetadataBearer {}
declare const EnableKinesisStreamingDestinationCommand_base: {
  new (
    input: EnableKinesisStreamingDestinationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    EnableKinesisStreamingDestinationCommandInput,
    EnableKinesisStreamingDestinationCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: EnableKinesisStreamingDestinationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    EnableKinesisStreamingDestinationCommandInput,
    EnableKinesisStreamingDestinationCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class EnableKinesisStreamingDestinationCommand extends EnableKinesisStreamingDestinationCommand_base {
  protected static __types: {
    api: {
      input: KinesisStreamingDestinationInput;
      output: KinesisStreamingDestinationOutput;
    };
    sdk: {
      input: EnableKinesisStreamingDestinationCommandInput;
      output: EnableKinesisStreamingDestinationCommandOutput;
    };
  };
}
