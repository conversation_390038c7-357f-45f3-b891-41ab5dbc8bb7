import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import { DescribeExportInput, DescribeExportOutput } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DescribeExportCommandInput extends DescribeExportInput {}
export interface DescribeExportCommandOutput
  extends DescribeExportOutput,
    __MetadataBearer {}
declare const DescribeExportCommand_base: {
  new (
    input: DescribeExportCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeExportCommandInput,
    DescribeExportCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeExportCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeExportCommandInput,
    DescribeExportCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeExportCommand extends DescribeExportCommand_base {
  protected static __types: {
    api: {
      input: DescribeExportInput;
      output: DescribeExportOutput;
    };
    sdk: {
      input: DescribeExportCommandInput;
      output: DescribeExportCommandOutput;
    };
  };
}
