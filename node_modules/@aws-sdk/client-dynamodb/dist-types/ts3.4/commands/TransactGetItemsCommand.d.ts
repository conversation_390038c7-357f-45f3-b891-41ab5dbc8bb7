import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import {
  TransactGetItemsInput,
  TransactGetItemsOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface TransactGetItemsCommandInput extends TransactGetItemsInput {}
export interface TransactGetItemsCommandOutput
  extends TransactGetItemsOutput,
    __MetadataBearer {}
declare const TransactGetItemsCommand_base: {
  new (
    input: TransactGetItemsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    TransactGetItemsCommandInput,
    TransactGetItemsCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: TransactGetItemsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    TransactGetItemsCommandInput,
    TransactGetItemsCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class TransactGetItemsCommand extends TransactGetItemsCommand_base {
  protected static __types: {
    api: {
      input: TransactGetItemsInput;
      output: TransactGetItemsOutput;
    };
    sdk: {
      input: TransactGetItemsCommandInput;
      output: TransactGetItemsCommandOutput;
    };
  };
}
