import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import {
  DescribeKinesisStreamingDestinationInput,
  DescribeKinesisStreamingDestinationOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DescribeKinesisStreamingDestinationCommandInput
  extends DescribeKinesisStreamingDestinationInput {}
export interface DescribeKinesisStreamingDestinationCommandOutput
  extends DescribeKinesisStreamingDestinationOutput,
    __MetadataBearer {}
declare const DescribeKinesisStreamingDestinationCommand_base: {
  new (
    input: DescribeKinesisStreamingDestinationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeKinesisStreamingDestinationCommandInput,
    DescribeKinesisStreamingDestinationCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeKinesisStreamingDestinationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeKinesisStreamingDestinationCommandInput,
    DescribeKinesisStreamingDestinationCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeKinesisStreamingDestinationCommand extends DescribeKinesisStreamingDestinationCommand_base {
  protected static __types: {
    api: {
      input: DescribeKinesisStreamingDestinationInput;
      output: DescribeKinesisStreamingDestinationOutput;
    };
    sdk: {
      input: DescribeKinesisStreamingDestinationCommandInput;
      output: DescribeKinesisStreamingDestinationCommandOutput;
    };
  };
}
