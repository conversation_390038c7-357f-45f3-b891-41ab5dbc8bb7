"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  getQueueUrlPlugin: () => getQueueUrlPlugin,
  getReceiveMessagePlugin: () => getReceiveMessagePlugin,
  getSendMessageBatchPlugin: () => getSendMessageBatchPlugin,
  getSendMessagePlugin: () => getSendMessagePlugin,
  queueUrlMiddleware: () => queueUrlMiddleware,
  queueUrlMiddlewareOptions: () => queueUrlMiddlewareOptions,
  receiveMessageMiddleware: () => receiveMessageMiddleware,
  receiveMessageMiddlewareOptions: () => receiveMessageMiddlewareOptions,
  resolveQueueUrlConfig: () => resolveQueueUrlConfig,
  sendMessageBatchMiddleware: () => sendMessageBatchMiddleware,
  sendMessageBatchMiddlewareOptions: () => sendMessageBatchMiddlewareOptions,
  sendMessageMiddleware: () => sendMessageMiddleware,
  sendMessageMiddlewareOptions: () => sendMessageMiddlewareOptions
});
module.exports = __toCommonJS(index_exports);

// src/queue-url.ts
var import_smithy_client = require("@smithy/smithy-client");
var resolveQueueUrlConfig = /* @__PURE__ */ __name((config) => {
  return Object.assign(config, {
    useQueueUrlAsEndpoint: config.useQueueUrlAsEndpoint ?? true
  });
}, "resolveQueueUrlConfig");
function queueUrlMiddleware({ useQueueUrlAsEndpoint, endpoint }) {
  return (next, context) => {
    return async (args) => {
      const { input } = args;
      const resolvedEndpoint = context.endpointV2;
      if (!endpoint && input.QueueUrl && resolvedEndpoint && useQueueUrlAsEndpoint) {
        const logger = context.logger instanceof import_smithy_client.NoOpLogger || !context.logger?.warn ? console : context.logger;
        try {
          const queueUrl = new URL(input.QueueUrl);
          const queueUrlOrigin = new URL(queueUrl.origin);
          if (resolvedEndpoint.url.origin !== queueUrlOrigin.origin) {
            logger.warn(
              `QueueUrl=${input.QueueUrl} differs from SQSClient resolved endpoint=${resolvedEndpoint.url.toString()}, using QueueUrl host as endpoint.
Set [endpoint=string] or [useQueueUrlAsEndpoint=false] on the SQSClient.`
            );
            context.endpointV2 = {
              ...resolvedEndpoint,
              url: queueUrlOrigin
            };
          }
        } catch (e) {
          logger.warn(e);
        }
      }
      return next(args);
    };
  };
}
__name(queueUrlMiddleware, "queueUrlMiddleware");
var queueUrlMiddlewareOptions = {
  name: "queueUrlMiddleware",
  relation: "after",
  toMiddleware: "endpointV2Middleware",
  override: true
};
var getQueueUrlPlugin = /* @__PURE__ */ __name((config) => ({
  applyToStack: /* @__PURE__ */ __name((clientStack) => {
    clientStack.addRelativeTo(queueUrlMiddleware(config), queueUrlMiddlewareOptions);
  }, "applyToStack")
}), "getQueueUrlPlugin");

// src/receive-message.ts
var import_util_hex_encoding = require("@smithy/util-hex-encoding");
var import_util_utf8 = require("@smithy/util-utf8");
function receiveMessageMiddleware(options) {
  return (next) => async (args) => {
    const resp = await next({ ...args });
    if (options.md5 === false) {
      return resp;
    }
    const output = resp.output;
    const messageIds = [];
    if (output.Messages !== void 0) {
      for (const message of output.Messages) {
        const md5 = message.MD5OfBody;
        const hash = new options.md5();
        hash.update((0, import_util_utf8.toUint8Array)(message.Body || ""));
        if (md5 !== (0, import_util_hex_encoding.toHex)(await hash.digest())) {
          messageIds.push(message.MessageId);
        }
      }
    }
    if (messageIds.length > 0) {
      throw new Error("Invalid MD5 checksum on messages: " + messageIds.join(", "));
    }
    return resp;
  };
}
__name(receiveMessageMiddleware, "receiveMessageMiddleware");
var receiveMessageMiddlewareOptions = {
  step: "initialize",
  tags: ["VALIDATE_BODY_MD5"],
  name: "receiveMessageMiddleware",
  override: true
};
var getReceiveMessagePlugin = /* @__PURE__ */ __name((config) => ({
  applyToStack: /* @__PURE__ */ __name((clientStack) => {
    clientStack.add(receiveMessageMiddleware(config), receiveMessageMiddlewareOptions);
  }, "applyToStack")
}), "getReceiveMessagePlugin");

// src/send-message.ts

var import_util_utf82 = require("@smithy/util-utf8");
var sendMessageMiddleware = /* @__PURE__ */ __name((options) => (next) => async (args) => {
  const resp = await next({ ...args });
  if (options.md5 === false) {
    return resp;
  }
  const output = resp.output;
  const hash = new options.md5();
  hash.update((0, import_util_utf82.toUint8Array)(args.input.MessageBody || ""));
  if (output.MD5OfMessageBody !== (0, import_util_hex_encoding.toHex)(await hash.digest())) {
    throw new Error("InvalidChecksumError");
  }
  return resp;
}, "sendMessageMiddleware");
var sendMessageMiddlewareOptions = {
  step: "initialize",
  tags: ["VALIDATE_BODY_MD5"],
  name: "sendMessageMiddleware",
  override: true
};
var getSendMessagePlugin = /* @__PURE__ */ __name((config) => ({
  applyToStack: /* @__PURE__ */ __name((clientStack) => {
    clientStack.add(sendMessageMiddleware(config), sendMessageMiddlewareOptions);
  }, "applyToStack")
}), "getSendMessagePlugin");

// src/send-message-batch.ts

var import_util_utf83 = require("@smithy/util-utf8");
var sendMessageBatchMiddleware = /* @__PURE__ */ __name((options) => (next) => async (args) => {
  const resp = await next({ ...args });
  if (options.md5 === false) {
    return resp;
  }
  const output = resp.output;
  const messageIds = [];
  const entries = {};
  if (output.Successful !== void 0) {
    for (const entry of output.Successful) {
      if (entry.Id !== void 0) {
        entries[entry.Id] = entry;
      }
    }
  }
  for (const entry of args.input.Entries) {
    if (entries[entry.Id]) {
      const md5 = entries[entry.Id].MD5OfMessageBody;
      const hash = new options.md5();
      hash.update((0, import_util_utf83.toUint8Array)(entry.MessageBody || ""));
      if (md5 !== (0, import_util_hex_encoding.toHex)(await hash.digest())) {
        messageIds.push(entries[entry.Id].MessageId);
      }
    }
  }
  if (messageIds.length > 0) {
    throw new Error("Invalid MD5 checksum on messages: " + messageIds.join(", "));
  }
  return resp;
}, "sendMessageBatchMiddleware");
var sendMessageBatchMiddlewareOptions = {
  step: "initialize",
  tags: ["VALIDATE_BODY_MD5"],
  name: "sendMessageBatchMiddleware",
  override: true
};
var getSendMessageBatchPlugin = /* @__PURE__ */ __name((config) => ({
  applyToStack: /* @__PURE__ */ __name((clientStack) => {
    clientStack.add(sendMessageBatchMiddleware(config), sendMessageBatchMiddlewareOptions);
  }, "applyToStack")
}), "getSendMessageBatchPlugin");
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  resolveQueueUrlConfig,
  queueUrlMiddlewareOptions,
  getQueueUrlPlugin,
  queueUrlMiddleware,
  receiveMessageMiddlewareOptions,
  getReceiveMessagePlugin,
  receiveMessageMiddleware,
  sendMessageMiddleware,
  sendMessageMiddlewareOptions,
  getSendMessagePlugin,
  sendMessageBatchMiddleware,
  sendMessageBatchMiddlewareOptions,
  getSendMessageBatchPlugin
});

